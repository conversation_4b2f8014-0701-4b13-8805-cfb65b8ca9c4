import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Get the base directory of the application
basedir = os.path.abspath(os.path.dirname(__file__))

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'you-will-never-guess'

    # MSSQL Database configuration
    DB_SERVER = os.environ.get('DEPLOY_SQL_SERVER')
    DB_NAME = os.environ.get('DEPLOY_SQL_DATABASE')
    DB_USER = os.environ.get('DEPLOY_SQL_USERNAME')
    DB_PASSWORD = os.environ.get('DEPLOY_SQL_PASSWORD')
    DB_DRIVER = os.environ.get('DEPLOY_DRIVER')

    # Connection string for MSSQL using pyodbc
    SQLALCHEMY_DATABASE_URI = f'mssql+pyodbc://{DB_USER}:{DB_PASSWORD}@{DB_SERVER}/{DB_NAME}?driver={DB_DRIVER.replace("{", "").replace("}", "")}'

    # Fallback to SQLite if MSSQL configuration is not available
    if not all([DB_SERVER, DB_NAME, DB_USER, DB_PASSWORD, DB_DRIVER]):
        SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
            'sqlite:///' + os.path.join(basedir, 'app.db')

    # Disable tracking modifications of objects
    SQLALCHEMY_TRACK_MODIFICATIONS = False
