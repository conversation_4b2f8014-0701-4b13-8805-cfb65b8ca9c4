{% extends "base.html" %}

{% block title %}Home{% endblock %}

{% block content %}
<div class="bg-white p-6 rounded-lg shadow-md">
    <h1 class="text-3xl font-bold text-blue-600 mb-4">Welcome to Deploy Project</h1>

    <p class="mb-4">This is a Python web application with Tailwind CSS and MSSQL database integration.</p>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
        <div class="bg-blue-100 p-4 rounded-lg">
            <h2 class="text-xl font-semibold text-blue-700 mb-2">Python Backend</h2>
            <p>Built with Flask, a lightweight WSGI web application framework.</p>
        </div>

        <div class="bg-green-100 p-4 rounded-lg">
            <h2 class="text-xl font-semibold text-green-700 mb-2">Tailwind CSS</h2>
            <p>A utility-first CSS framework for rapidly building custom user interfaces.</p>
        </div>

        <div class="bg-purple-100 p-4 rounded-lg">
            <h2 class="text-xl font-semibold text-purple-700 mb-2">MSSQL Database</h2>
            <p>Microsoft SQL Server integration for robust data storage.</p>
        </div>
    </div>

    <div class="mt-8 space-x-4">
        <a href="{{ url_for('main.users') }}" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
            Xem người dùng
        </a>

        <a href="{{ url_for('main.history') }}" class="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700">
            Lịch sử Deploy
        </a>
    </div>
</div>
{% endblock %}
