/* Styles for History page */

/* Table styles */
table {
    table-layout: fixed;
    width: 100%;
}

/* Column widths */
.deploy-column {
    width: 300px;
}

.time-column {
    width: 100px;
    white-space: nowrap;
    text-align: center;
}

.number-column {
    width: 80px;
    white-space: nowrap;
    text-align: center;
}

.branch-column {
    width: 250px;
    white-space: normal; /* Cho phép xuống dòng */
    word-break: break-word; /* Ngắt từ khi cần thiết */
}

.message-column {
    width: auto;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Deploy items styles */
.deploy-items {
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
}

.deploy-item {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    margin-right: 0.5rem;
    min-width: 80px;
    height: 28px;
    text-align: center;
}

/* Project styles */
.project-tag {
    background-color: #dbeafe;
    color: #1e40af;
}

/* Warehouse styles */
.warehouse-alsw {
    background-color: #dcfce7; /* Green */
    color: #166534;
}

.warehouse-alsb {
    background-color: #dbeafe; /* Blue */
    color: #1e40af;
}

.warehouse-clc {
    background-color: #fef9c3; /* Yellow */
    color: #854d0e;
}

.warehouse-alse {
    background-color: #ffedd5; /* Orange */
    color: #9a3412;
}

/* Environment styles */
.env-stable {
    background-color: #dcfce7;
    color: #166534;
}

.env-uat2 {
    background-color: #dbeafe;
    color: #1e40af;
}

.env-uat3 {
    background-color: #fef9c3;
    color: #854d0e;
}

.env-uat4 {
    background-color: #ffedd5;
    color: #9a3412;
}

.env-uat5 {
    background-color: #fee2e2;
    color: #991b1b;
}

.env-other {
    background-color: #f3e8ff;
    color: #6b21a8;
}

/* Action column styles */
.action-column {
    width: 120px;
}

.action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #f3f4f6;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background-color: #e5e7eb;
}

.view-btn {
    color: #3b82f6;
}

.view-btn:hover {
    background-color: #dbeafe;
}

.edit-btn {
    color: #10b981;
}

.edit-btn:hover {
    background-color: #d1fae5;
}

.delete-btn {
    color: #ef4444;
}

.delete-btn:hover {
    background-color: #fee2e2;
}

/* Modal styles */
.modal {
    display: none; /* Hidden by default */
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.4);
    transition: opacity 0.25s ease;
}

.modal-content {
    background-color: #fefefe;
    margin: 0 auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    max-width: 800px;
    border-radius: 8px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
    position: relative;
    top: 50%;
    transform: translateY(-50%);
}

/* Show the modal */
.modal-show {
    display: block !important;
    animation: fadeIn 0.3s ease-out;
}

.modal-show .modal-content {
    animation: slideIn 0.3s ease-out;
}

/* Animation for modal */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-60%); }
    to { transform: translateY(-50%); }
}

/* Pagination styles */
.pagination-container {
    margin-top: 1.5rem;
}

.pagination-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.375rem;
    background-color: #f3f4f6;
    color: #4b5563;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.pagination-btn:hover {
    background-color: #e5e7eb;
    color: #1f2937;
}

.pagination-active {
    background-color: #3b82f6;
    color: white;
}

.pagination-active:hover {
    background-color: #2563eb;
    color: white;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
}

/* Per page select styles */
.per-page-select {
    appearance: none;
    background-color: #f3f4f6;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    padding: 0.375rem 2rem 0.375rem 0.75rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    color: #4b5563;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    cursor: pointer;
}

.per-page-select:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
    border-color: #3b82f6;
}

/* Code block styles */
.message-container {
    position: relative;
}

#modal-message {
    max-height: 200px;
    overflow-y: auto;
    line-height: 1.5;
    border-left: 4px solid #4b5563;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

/* Copy button styles */
.copy-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border-radius: 4px;
    padding: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
}

.copy-btn:hover {
    background-color: rgba(255, 255, 255, 0.3);
}

.copy-btn:active {
    transform: scale(0.95);
}
