// JavaScript for History page
document.addEventListener('DOMContentLoaded', function() {
    // Xử lý sự kiện khi thay đổi giá trị trong dropdown
    const projectDropdown = document.getElementById('project');
    const warehouseDropdown = document.getElementById('warehouse');
    const environmentDropdown = document.getElementById('environment');
    const filterNotInPrdBtn = document.getElementById('filterNotInPrdBtn');

    // Kiểm tra trạng thái nút "Lọc Commit chưa lên PRD"
    function updateFilterNotInPrdButtonState() {
        if (filterNotInPrdBtn) {
            const projectValue = projectDropdown ? projectDropdown.value : '';
            const warehouseValue = warehouseDropdown ? warehouseDropdown.value : '';

            // Chỉ kích hoạt nút khi cả Project và Warehouse đã được chọn
            if (projectValue && projectValue !== 'all' && warehouseValue && warehouseValue !== 'all') {
                filterNotInPrdBtn.disabled = false;
            } else {
                filterNotInPrdBtn.disabled = true;
            }
        }
    }

    // Cập nhật trạng thái nút khi trang được tải
    updateFilterNotInPrdButtonState();

    if (projectDropdown) {
        projectDropdown.addEventListener('change', function() {
            updateFilterNotInPrdButtonState();
            document.querySelector('form').submit();
        });
    }

    if (warehouseDropdown) {
        warehouseDropdown.addEventListener('change', function() {
            updateFilterNotInPrdButtonState();
            document.querySelector('form').submit();
        });
    }

    if (environmentDropdown) {
        environmentDropdown.addEventListener('change', function() {
            document.querySelector('form').submit();
        });
    }

    // Xử lý sự kiện khi nhấn nút "Lọc Commit chưa lên PRD"
    if (filterNotInPrdBtn) {
        filterNotInPrdBtn.addEventListener('click', function() {
            if (this.disabled) return;

            const projectValue = projectDropdown ? projectDropdown.value : '';
            const warehouseValue = warehouseDropdown ? warehouseDropdown.value : '';

            // Hiển thị modal và tải dữ liệu
            const notInPrdModal = document.getElementById('notInPrdModal');
            if (notInPrdModal) {
                // Cập nhật thông tin Project và Warehouse trong modal
                const projectElement = document.getElementById('not-in-prd-project');
                const warehouseElement = document.getElementById('not-in-prd-warehouse');

                if (projectElement) {
                    projectElement.textContent = projectValue;
                    projectElement.className = 'deploy-item project-tag';
                }

                if (warehouseElement) {
                    warehouseElement.textContent = warehouseValue;
                    warehouseElement.className = 'deploy-item';

                    // Thêm class tương ứng với warehouse
                    if (warehouseValue === 'ALSW') {
                        warehouseElement.classList.add('warehouse-alsw');
                    } else if (warehouseValue === 'ALSB') {
                        warehouseElement.classList.add('warehouse-alsb');
                    } else if (warehouseValue === 'CLC') {
                        warehouseElement.classList.add('warehouse-clc');
                    } else if (warehouseValue === 'ALSE') {
                        warehouseElement.classList.add('warehouse-alse');
                    } else {
                        warehouseElement.classList.add('warehouse-alsw');
                    }
                }

                // Hiển thị trạng thái đang tải
                const tableBody = document.getElementById('not-in-prd-table-body');
                const messageElement = document.getElementById('not-in-prd-message');

                if (tableBody) {
                    tableBody.innerHTML = '<tr><td colspan="4" class="py-2 px-3 border-b text-center text-gray-500">Đang tải dữ liệu...</td></tr>';
                }

                if (messageElement) {
                    messageElement.textContent = 'Đang tải dữ liệu...';
                }

                // Gọi API để lấy dữ liệu
                fetch(`/api/commits-not-in-prd?project=${encodeURIComponent(projectValue)}&warehouse=${encodeURIComponent(warehouseValue)}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Nếu có thông báo, hiển thị nó
                            if (data.message) {
                                if (tableBody) {
                                    tableBody.innerHTML = `<tr><td colspan="4" class="py-2 px-3 border-b text-center text-gray-500">${data.message}</td></tr>`;
                                }

                                if (messageElement) {
                                    messageElement.textContent = data.message;
                                }
                                return;
                            }

                            // Nếu có dữ liệu commit
                            if (data.commits && data.commits.length > 0) {
                                // Tổng hợp tất cả message
                                // Lọc những message trùng lặp
                                const uniqueMessages = new Set();
                                data.commits
                                    .filter(commit => commit.message)
                                    .forEach(commit => uniqueMessages.add(commit.message));
                                
                                const allMessages = Array.from(uniqueMessages).join('\n\n');

                                if (messageElement) {
                                    messageElement.textContent = allMessages || 'Không có thông tin message.';
                                }

                                // Hiển thị dữ liệu commit trong bảng
                                let tableHtml = '';
                                data.commits.forEach(commit => {
                                    tableHtml += `
                                        <tr>
                                            <td class="py-2 px-3 border-b text-center">${commit.stt}</td>
                                            <td class="py-2 px-3 border-b font-mono text-sm">${commit.commit_sha}</td>
                                            <td class="py-2 px-3 border-b text-center">
                                                ${commit.uat
                                                    ? '<svg class="h-5 w-5 text-green-500 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>'
                                                    : '<svg class="h-5 w-5 text-red-500 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>'}
                                            </td>
                                            <td class="py-2 px-3 border-b text-center">
                                                ${commit.prd
                                                    ? '<svg class="h-5 w-5 text-green-500 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>'
                                                    : '<svg class="h-5 w-5 text-red-500 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>'}
                                            </td>
                                        </tr>
                                    `;
                                });

                                if (tableBody) {
                                    tableBody.innerHTML = tableHtml;
                                }
                            } else {
                                // Không có dữ liệu
                                if (tableBody) {
                                    tableBody.innerHTML = '<tr><td colspan="4" class="py-2 px-3 border-b text-center text-gray-500">Không có dữ liệu commit</td></tr>';
                                }

                                if (messageElement) {
                                    messageElement.textContent = 'Không có dữ liệu commit.';
                                }
                            }
                        } else {
                            // Lỗi
                            if (tableBody) {
                                tableBody.innerHTML = `<tr><td colspan="4" class="py-2 px-3 border-b text-center text-red-500">${data.error || 'Lỗi khi tải dữ liệu'}</td></tr>`;
                            }

                            if (messageElement) {
                                messageElement.textContent = data.error || 'Lỗi khi tải dữ liệu.';
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching commits not in PRD:', error);

                        if (tableBody) {
                            tableBody.innerHTML = '<tr><td colspan="4" class="py-2 px-3 border-b text-center text-red-500">Lỗi khi tải dữ liệu</td></tr>';
                        }

                        if (messageElement) {
                            messageElement.textContent = 'Lỗi khi tải dữ liệu.';
                        }
                    });

                // Hiển thị modal
                notInPrdModal.classList.add('modal-show');

                // Xử lý nút copy to clipboard cho modal Commit chưa lên PRD
                const copyNotInPrdMessageBtn = document.getElementById('copy-not-in-prd-message-btn');
                if (copyNotInPrdMessageBtn) {
                    copyNotInPrdMessageBtn.addEventListener('click', function() {
                        const messageElement = document.getElementById('not-in-prd-message');
                        if (messageElement) {
                            // Lấy nội dung text từ phần tử
                            const textToCopy = messageElement.textContent;

                            // Sử dụng Clipboard API để copy text
                            navigator.clipboard.writeText(textToCopy)
                                .then(() => {
                                    // Hiển thị thông báo thành công
                                    const originalTitle = this.title;
                                    const originalInnerHTML = this.innerHTML;

                                    // Thay đổi icon và title khi copy thành công
                                    this.title = 'Copied!';
                                    this.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>';

                                    // Khôi phục lại sau 2 giây
                                    setTimeout(() => {
                                        this.title = originalTitle;
                                        this.innerHTML = originalInnerHTML;
                                    }, 2000);
                                })
                                .catch(err => {
                                    console.error('Could not copy text: ', err);
                                    alert('Không thể copy nội dung. Vui lòng thử lại.');
                                });
                        }
                    });
                }
            }
        });
    }

    // Xử lý dropdown chọn số bản ghi mỗi trang
    const perPageSelect = document.getElementById('per-page-select');
    if (perPageSelect) {
        perPageSelect.addEventListener('change', function() {
            // Lấy URL hiện tại
            const url = new URL(window.location.href);

            // Cập nhật tham số per_page
            url.searchParams.set('per_page', this.value);

            // Reset về trang 1 khi thay đổi số bản ghi mỗi trang
            url.searchParams.set('page', '1');

            // Chuyển hướng đến URL mới
            window.location.href = url.toString();
        });
    }

    // Xử lý modal
    const modal = document.getElementById('detailModal');
    console.log('Modal element:', modal);

    const closeModal = document.getElementById('closeModal');
    console.log('Close modal button:', closeModal);

    const closeModalBtn = document.getElementById('closeModalBtn');
    console.log('Close modal button 2:', closeModalBtn);

    const viewButtons = document.querySelectorAll('.view-btn');
    console.log('View buttons:', viewButtons, viewButtons.length);

    // Xử lý nút copy to clipboard
    const copyMessageBtn = document.getElementById('copy-message-btn');
    if (copyMessageBtn) {
        copyMessageBtn.addEventListener('click', function() {
            const messageElement = document.getElementById('modal-message');
            if (messageElement) {
                // Lấy nội dung text từ phần tử
                const textToCopy = messageElement.textContent;

                // Sử dụng Clipboard API để copy text
                navigator.clipboard.writeText(textToCopy)
                    .then(() => {
                        // Hiển thị thông báo thành công
                        const originalTitle = this.title;
                        const originalInnerHTML = this.innerHTML;

                        // Thay đổi icon và title khi copy thành công
                        this.title = 'Copied!';
                        this.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>';

                        // Khôi phục lại sau 2 giây
                        setTimeout(() => {
                            this.title = originalTitle;
                            this.innerHTML = originalInnerHTML;
                        }, 2000);
                    })
                    .catch(err => {
                        console.error('Could not copy text: ', err);
                        alert('Không thể copy nội dung. Vui lòng thử lại.');
                    });
            }
        });
    }

    // Chỉ thêm event listeners nếu các phần tử tồn tại
    if (modal && closeModal && closeModalBtn) {
        // Đóng modal khi click vào nút đóng
        closeModal.addEventListener('click', function() {
            modal.classList.remove('modal-show');
        });

        closeModalBtn.addEventListener('click', function() {
            modal.classList.remove('modal-show');
        });

        // Không đóng modal khi click bên ngoài, chỉ đóng khi nhấn nút Đóng
        // Đoạn code này đã được loại bỏ theo yêu cầu
    }

    // Xử lý đóng modal Commit chưa lên PRD
    const notInPrdModal = document.getElementById('notInPrdModal');
    const closeNotInPrdModal = document.getElementById('closeNotInPrdModal');
    const closeNotInPrdModalBtn = document.getElementById('closeNotInPrdModalBtn');

    if (notInPrdModal && closeNotInPrdModal && closeNotInPrdModalBtn) {
        // Đóng modal khi click vào nút đóng
        closeNotInPrdModal.addEventListener('click', function() {
            notInPrdModal.classList.remove('modal-show');
        });

        closeNotInPrdModalBtn.addEventListener('click', function() {
            notInPrdModal.classList.remove('modal-show');
        });
    }

    // Xử lý sự kiện khi click vào nút xem
    if (viewButtons && viewButtons.length > 0) {
        viewButtons.forEach(function(button, index) {
            button.addEventListener('click', function() {
            // Lấy dữ liệu từ hàng tương ứng
            const row = this.closest('tr');
            const deployItems = row.querySelectorAll('.deploy-item');

            // Lấy thông tin từ các cột (đã thay đổi thứ tự)
            const time = row.cells[0].textContent.trim();
            const project = deployItems[0].textContent.trim();
            const warehouse = deployItems[1].textContent.trim();
            const environment = deployItems[2].textContent.trim();
            const branchNumber = row.cells[2].textContent.trim();
            const branch = row.cells[3].textContent.trim();
            const message = row.cells[4].textContent.trim();

            // Lấy thông tin chi tiết từ data attributes (sẽ được thêm vào)
            const lastBranch = row.dataset.lastBranch || 'N/A';
            const commitSha = row.dataset.commitSha || 'N/A';
            const fileName = row.dataset.fileName || 'N/A';
            const errorLog = row.dataset.errorLog || '';

            // Cập nhật nội dung modal - kiểm tra sự tồn tại của các phần tử trước
            const projectElement = document.getElementById('modal-project');
            const warehouseElement = document.getElementById('modal-warehouse');
            const envElement = document.getElementById('modal-environment');
            const branchNumberElement = document.getElementById('modal-branch-number');
            const timeElement = document.getElementById('modal-time');
            const branchElement = document.getElementById('modal-branch');
            const lastBranchElement = document.getElementById('modal-last-branch');
            const fileNameElement = document.getElementById('modal-file-name');
            const messageElement = document.getElementById('modal-message');
            const commitShaElement = document.getElementById('modal-commit-sha');
            const errorLogSection = document.getElementById('error-log-section');
            const errorLogElement = document.getElementById('modal-error-log');

            // Kiểm tra và cập nhật từng phần tử
            if (projectElement) projectElement.textContent = project;

            // Xử lý màu cho warehouse
            if (warehouseElement) {
                warehouseElement.textContent = warehouse;

                // Xóa tất cả các class cũ
                warehouseElement.className = 'deploy-item';

                // Thêm class tương ứng với warehouse
                if (warehouse === 'ALSW') {
                    warehouseElement.classList.add('warehouse-alsw');
                } else if (warehouse === 'ALSB') {
                    warehouseElement.classList.add('warehouse-alsb');
                } else if (warehouse === 'CLC') {
                    warehouseElement.classList.add('warehouse-clc');
                } else if (warehouse === 'ALSE') {
                    warehouseElement.classList.add('warehouse-alse');
                } else {
                    warehouseElement.classList.add('warehouse-alsw');
                }
            }

            // Xử lý màu cho environment
            if (envElement) {
                envElement.textContent = environment;

                // Xóa tất cả các class cũ
                envElement.className = 'deploy-item';

                // Thêm class tương ứng với environment
                if (environment === 'STABLE') {
                    envElement.classList.add('env-stable');
                } else if (environment === 'UAT2') {
                    envElement.classList.add('env-uat2');
                } else if (environment === 'UAT3') {
                    envElement.classList.add('env-uat3');
                } else if (environment === 'UAT4') {
                    envElement.classList.add('env-uat4');
                } else if (environment === 'UAT5') {
                    envElement.classList.add('env-uat5');
                } else {
                    envElement.classList.add('env-other');
                }
            }

            // Cập nhật các phần tử khác
            if (branchNumberElement) branchNumberElement.textContent = branchNumber;
            if (timeElement) timeElement.textContent = time;
            if (branchElement) branchElement.textContent = branch;
            if (lastBranchElement) lastBranchElement.textContent = lastBranch;
            if (fileNameElement) fileNameElement.textContent = fileName;
            // Giữ nguyên dấu xuống dòng trong message
            if (messageElement) {
                // Thay thế dấu xuống dòng bằng thẻ <br> nếu cần
                messageElement.textContent = message;
            }
            if (commitShaElement) commitShaElement.textContent = commitSha;

            // Hiển thị hoặc ẩn phần error log
            if (errorLogSection && errorLogElement) {
                if (errorLog && errorLog.trim() !== '') {
                    errorLogElement.textContent = errorLog;
                    errorLogSection.classList.remove('hidden');
                } else {
                    errorLogSection.classList.add('hidden');
                }
            }

            // Lấy dữ liệu commit từ API
            const historyId = row.dataset.id || '';
            if (historyId) {
                // Hiển thị trạng thái đang tải
                const commitTableBody = document.getElementById('commit-table-body');
                if (commitTableBody) {
                    commitTableBody.innerHTML = '<tr><td colspan="4" class="py-2 px-3 border-b text-center text-gray-500">Đang tải dữ liệu...</td></tr>';
                }

                // Gọi API để lấy dữ liệu commit
                fetch(`/api/commits/${historyId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.commits && data.commits.length > 0) {
                            // Hiển thị dữ liệu commit trong bảng
                            let tableHtml = '';
                            data.commits.forEach(commit => {
                                tableHtml += `
                                    <tr>
                                        <td class="py-2 px-3 border-b text-center">${commit.stt}</td>
                                        <td class="py-2 px-3 border-b font-mono text-sm">${commit.commit_sha}</td>
                                        <td class="py-2 px-3 border-b text-center">
                                            ${commit.uat
                                                ? '<svg class="h-5 w-5 text-green-500 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>'
                                                : '<svg class="h-5 w-5 text-red-500 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>'}
                                        </td>
                                        <td class="py-2 px-3 border-b text-center">
                                            ${commit.prd
                                                ? '<svg class="h-5 w-5 text-green-500 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>'
                                                : '<svg class="h-5 w-5 text-red-500 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>'}
                                        </td>
                                    </tr>
                                `;
                            });

                            if (commitTableBody) {
                                commitTableBody.innerHTML = tableHtml;
                            }
                        } else {
                            // Hiển thị thông báo không có dữ liệu
                            if (commitTableBody) {
                                commitTableBody.innerHTML = '<tr><td colspan="4" class="py-2 px-3 border-b text-center text-gray-500">Không có dữ liệu commit</td></tr>';
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching commits:', error);
                        // Hiển thị thông báo lỗi
                        if (commitTableBody) {
                            commitTableBody.innerHTML = '<tr><td colspan="4" class="py-2 px-3 border-b text-center text-red-500">Lỗi khi tải dữ liệu commit</td></tr>';
                        }
                    });
            }

            // Hiển thị modal nếu nó tồn tại
            if (modal) {
                modal.classList.add('modal-show');
                console.log('Modal should be visible now with class modal-show');
            }
        });
    });
    }
});

