# Deploy Project

Dự án web sử dụng Python, HTML, CSS (Tailwind) và MSSQL database.

## Tính năng

- Backend sử dụng Python Flask
- Frontend sử dụng Tailwind CSS
- Kết nối cơ sở dữ liệu MSSQL
- Thiế<PERSON> kế responsive

## Yêu cầu

- Python 3.8+
- MSSQL Server
- ODBC Driver cho SQL Server

## Cài đặt

1. Clone repository:
   ```
   git clone https://github.com/yourusername/deploy.git
   cd deploy
   ```

2. Tạo môi trường ảo:
   ```
   python -m venv venv
   ```

3. <PERSON><PERSON>ch hoạt môi trường ảo:
   - Windows:
     ```
     venv\Scripts\activate
     ```
   - macOS/Linux:
     ```
     source venv/bin/activate
     ```

4. Cài đặt các gói phụ thuộc:
   ```
   pip install -r requirements.txt
   ```

5. Tạo file `.env` trong thư mục gốc của dự án với các biến sau:
   ```
   SECRET_KEY=your-secret-key
   DEPLOY_SQL_SERVER=your-server-name
   DEPLOY_SQL_DATABASE=your-database-name
   DEPLOY_SQL_USERNAME=your-username
   DEPLOY_SQL_PASSWORD=your-password
   DEPLOY_DRIVER={ODBC Driver 17 for SQL Server}
   ```

## Chạy ứng dụng

```
python run.py
```

Ứng dụng sẽ chạy tại http://localhost:5555

## Cấu trúc dự án

```
deploy/
├── app/
│   ├── __init__.py
│   ├── routes.py
│   ├── models.py
│   ├── database.py
│   ├── static/
│   │   ├── css/
│   │   ├── js/
│   │   └── img/
│   └── templates/
│       ├── base.html
│       ├── index.html
│       └── users.html
├── .gitignore
├── .env
├── requirements.txt
├── config.py
└── run.py
```

## Nhật ký phát triển

### 25/04/2025
- Khởi tạo dự án với cấu trúc cơ bản
- Tạo các file cấu hình và thiết lập môi trường
- Thiết lập kết nối cơ sở dữ liệu MSSQL
- Tạo các trang cơ bản: trang chủ, trang người dùng
- Thêm chức năng quản lý người dùng đơn giản
- Cấu hình ứng dụng chạy trên cổng 5555
- Cập nhật README.md với hướng dẫn cài đặt và sử dụng
- Commit và chuẩn bị push lên GitHub
- Thêm model History để lưu trữ thông tin các commit được cherrypick để deploy
- Tạo giao diện hiển thị lịch sử deploy với các nhóm theo Project, Warehouse và Environment
- Thêm chức năng lọc dữ liệu theo Project, Warehouse và Environment
- Thêm chức năng tạo dữ liệu mẫu cho bảng History
- Cập nhật menu điều hướng và trang chủ để thêm liên kết đến trang History
- Sửa lỗi hiển thị History và cải thiện xử lý khi không có dữ liệu
- Thêm giá trị mặc định cho Project, Warehouse và Environment
- Thay đổi thiết kế giao diện History từ dạng xếp tầng sang dạng bảng với 3 cột đầu là Project, Warehouse, Environment
- Đổi màu cho các loại Environment, với STABLE có màu xanh lá cây, UAT2-UAT5 có màu khác nhau để dễ phân biệt
- Gộp 3 cột Project, Warehouse và Environment thành một cột "Deploy" với các thẻ hiển thị cùng nhau trên một dòng
- Tạo file CSS riêng cho trang History để quản lý styles
- Bỏ cột ID và File, thêm cột BranchNumber (đặt tên là Number) để hiển thị số thứ tự của branch
- Đổi tên cột "Thời gian" thành "Time" và định dạng thời gian từ "25/04/2025 14:40" thành "25/04 14:40"
- Bỏ cột "Commit SHA" để làm gọn bảng
- Cải thiện CSS để tất cả nội dung hiển thị trên một dòng và bỏ scroll ngang
- Chỉnh sửa các thẻ trong cột Deploy để có kích thước bằng nhau
- Thêm cột Action với các icon tương đương với các nút xem, sửa, xóa
- Thêm chức năng mở modal dialog khi ấn vào nút xem trên trang History
- Tạo giao diện modal hiển thị chi tiết thông tin của bản ghi được chọn
- Thêm các trường dữ liệu chi tiết vào modal: Project, Warehouse, Environment, Branch Number, Time, Branch, Last Branch, File Name, Message, Commit SHA, Error Log
- Thêm animation cho modal khi mở và đóng
- Thêm các nút đóng modal và xử lý sự kiện khi click bên ngoài modal
- Cập nhật CSS để hiển thị modal đẹp hơn và responsive trên các kích thước màn hình
- Thêm data attributes vào các hàng trong bảng để lưu trữ thông tin chi tiết
- Sửa lỗi JavaScript khi không có dữ liệu trên trang history
- Sửa lỗi "Cannot set properties of null (setting 'textContent')" khi cập nhật nội dung modal
- Sửa lỗi "Cannot read properties of null (reading 'classList')" khi hiển thị modal
- Thêm kiểm tra null cho tất cả các phần tử DOM trước khi truy cập thuộc tính hoặc phương thức
- Sửa lỗi modal không hiển thị khi ấn vào nút xem bằng cách thay đổi cách hiển thị/ẩn modal
- Cải thiện CSS cho modal với class modal-show để đảm bảo hiển thị đúng
- Di chuyển modal vào bên trong block content để đảm bảo nó được render cùng với nội dung chính
- Loại bỏ chức năng đóng modal khi click bên ngoài, chỉ đóng khi nhấn nút Đóng
- Di chuyển cột Time lên đầu tiên trong bảng để dễ theo dõi
- Cải thiện hiển thị cột Branch để hiển thị đầy đủ nội dung thay vì cắt ngắn
- Giảm kích thước của cột Time từ 100px xuống 80px và căn giữa nội dung
- Giảm kích thước của cột Number xuống 60px và căn giữa nội dung
- Thêm chức năng phân trang cho trang history, hiển thị mặc định 10 bản ghi trên mỗi trang
- Thêm điều hướng phân trang với các nút First, Previous, Next, Last và số trang
- Hiển thị thông tin về tổng số bản ghi và số bản ghi đang hiển thị
- Thêm dropdown cho phép người dùng chọn số bản ghi hiển thị trên mỗi trang (5, 10, 20, 50, 100)
- Sửa lỗi URL phân trang khi chuyển trang với tham số per_page
- Đổi màu warehouse-tag, mỗi kho một màu khác nhau (ALSW: xanh lá, ALSB: xanh dương, CLC: vàng, ALSE: cam)
- Sửa lại bộ lọc tìm kiếm để kích thước dropdown to hơn, bằng với nút Lọc
- Thêm text box tìm kiếm để tìm kiếm nội dung trong Branch và Message
- Điều chỉnh ô tìm kiếm để làm cho nó dài hơn (w-80 = 320px)
- Thay đổi hiển thị commit SHA trong modal thành dạng bảng với các cột STT, SHA, UAT, PRD
- Sửa lại phương thức get_commits_for_history_id để chỉ lấy các commit liên quan trực tiếp đến History ID
- Bỏ nút và chức năng thêm dữ liệu mẫu
- Cải thiện hiển thị Message trong modal Chi tiết Deploy, định dạng dạng code và giữ nguyên dấu xuống dòng
- Thêm nút copy to clipboard ở góc trên cùng bên phải của khối Message trong modal Chi tiết Deploy
- Tách JavaScript từ file history.html ra file riêng (app/static/js/history.js) để dễ quản lý và bảo trì
- Cố định footer luôn ở dưới cùng của trình duyệt, xử lý cả trường hợp khi dữ liệu nhiều và có thanh cuộn dọc
- Thêm nút "Lọc Commit chưa lên PRD" bên phải nút "Đặt lại" để tìm kiếm các commit chưa lên PRD theo Project và Warehouse
- Thêm nút Copy to clipboard cho phần Message trong modal Commit chưa lên PRD
- Chỉnh sửa vị trí modal Commit chưa lên PRD và modal Chi tiết Deploy để căn giữa màn hình
- Thiết lập trang History làm trang chủ của ứng dụng, chuyển hướng từ URL gốc (/) đến trang History
- Cải thiện phương thức get_commits_not_in_prd để sắp xếp kết quả theo thứ tự tăng dần của commit.id
- Thêm hỗ trợ favicon đầy đủ cho website với các định dạng khác nhau (ico, png) và kích thước khác nhau (16x16, 32x32, 192x192, 512x512)
- Thêm Apple touch icon và Android chrome icon để hỗ trợ thiết bị di động
- Cập nhật site.webmanifest với thông tin ứng dụng và đường dẫn đúng
- Thêm meta theme-color để tùy chỉnh màu thanh địa chỉ trên thiết bị di động

## Hướng dẫn Git

### Khởi tạo repository trên GitHub
1. Truy cập vào GitHub (https://github.com)
2. Đăng nhập vào tài khoản của bạn
3. Nhấp vào nút "+" ở góc trên bên phải và chọn "New repository"
4. Đặt tên repository là "Deploy"
5. Thêm mô tả: "Dự án web sử dụng Python, HTML, CSS (Tailwind) và MSSQL database"
6. Chọn repository là Public hoặc Private tùy theo nhu cầu
7. Nhấp vào "Create repository"

### Push code lên GitHub
```
git push -u origin main
```

