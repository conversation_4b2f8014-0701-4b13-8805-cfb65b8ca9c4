from datetime import datetime
from app.database import db
from sqlalchemy import or_
import re
import logging
log = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s',
                    datefmt='%d/%m/%Y %H:%M:%S')

class User(db.Model):
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(100), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<User {self.username}>'

    @classmethod
    def get_all(cls):
        return cls.query.all()

class History(db.Model):
    __tablename__ = 'History'

    id = db.Column(db.Integer, primary_key=True)
    request_date_time = db.Column(db.DateTime, default=datetime.utcnow, name='RequestDateTime')
    branch = db.Column(db.String(200), name='Branch')
    last_branch = db.Column(db.String(200), name='LastBranch')
    message = db.Column(db.Text, name='Message')
    commit_sha = db.Column(db.Text, name='CommitSHA')
    error_log = db.Column(db.Text, name='ErrorLog')
    environment = db.Column(db.String(50), name='Environment')
    warehouse = db.Column(db.String(50), name='Warehouse')
    file_name = db.Column(db.String(200), name='FileName')
    branch_number = db.Column(db.Integer, name='BranchNumber')
    project = db.Column(db.String(50), name='Project')

    def __repr__(self):
        return f'<History {self.id} - {self.commit_sha}>'

    @classmethod
    def get_all(cls):
        return cls.query.all()

    @classmethod
    def get_by_project(cls, project):
        return cls.query.filter_by(project=project).all()

    @classmethod
    def get_by_warehouse(cls, warehouse):
        return cls.query.filter_by(warehouse=warehouse).all()

    @classmethod
    def get_by_environment(cls, environment):
        return cls.query.filter_by(environment=environment).all()

    @classmethod
    def get_all_data(cls, page=1, per_page=10, project=None, warehouse=None, environment=None, search_query=None):
        """Lấy dữ liệu History có phân trang

        Args:
            page (int): Trang hiện tại (bắt đầu từ 1)
            per_page (int): Số bản ghi trên mỗi trang
            project (str, optional): Lọc theo project
            warehouse (str, optional): Lọc theo warehouse
            environment (str, optional): Lọc theo environment
            search_query (str, optional): Tìm kiếm trong branch và message

        Returns:
            dict: Dữ liệu History có phân trang
        """
        # Đặt giá trị mặc định cho các danh sách
        default_projects = ['AOMS', 'APMS']
        default_warehouses = ['ALSW', 'ALSB', 'CLC', 'ALSE']
        default_environments = ['STABLE', 'UAT2', 'UAT3', 'UAT4', 'UAT5']

        # Lấy dữ liệu từ cơ sở dữ liệu
        projects_query = db.session.query(cls.project).distinct().all()
        projects = [p[0] for p in projects_query if p[0]] or default_projects

        warehouses_query = db.session.query(cls.warehouse).distinct().all()
        warehouses = [w[0] for w in warehouses_query if w[0]] or default_warehouses

        environments_query = db.session.query(cls.environment).distinct().all()
        environments = [e[0] for e in environments_query if e[0]] or default_environments

        # Tạo query cơ bản
        query = cls.query.order_by(cls.request_date_time.desc())

        # Áp dụng các bộ lọc
        if project:
            query = query.filter_by(project=project)

        if warehouse:
            query = query.filter_by(warehouse=warehouse)

        if environment:
            query = query.filter_by(environment=environment)

        # Áp dụng tìm kiếm
        if search_query and search_query.strip():
            search_term = f"%{search_query.strip()}%"
            query = query.filter(
                db.or_(
                    cls.branch.ilike(search_term),
                    cls.message.ilike(search_term)
                )
            )

        # Tính toán tổng số bản ghi và tổng số trang
        total_records = query.count()
        total_pages = (total_records + per_page - 1) // per_page  # Làm tròn lên

        # Lấy dữ liệu cho trang hiện tại
        records = query.limit(per_page).offset((page - 1) * per_page).all()

        return {
            'projects': projects,
            'warehouses': warehouses,
            'environments': environments,
            'records': records,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total_records': total_records,
                'total_pages': total_pages
            }
        }

class Commit(db.Model):
    __tablename__ = 'Commit'

    id = db.Column(db.Integer, primary_key=True)
    project = db.Column(db.String(50), name='Project')
    warehouse = db.Column(db.String(50), name='Warehouse')
    commit_sha = db.Column(db.String(200), name='CommitSHA')
    history_uat_id = db.Column(db.Integer, name='HistoryUATId')
    history_prd_id = db.Column(db.Integer, name='HistoryPRDId')

    def __repr__(self):
        return f'<Commit {self.id} - {self.commit_sha}>'

    @classmethod
    def get_commits_by_project_warehouse(cls, project, warehouse):
        """Lấy danh sách commit theo project và warehouse"""
        return cls.query.filter_by(project=project, warehouse=warehouse).all()

    @classmethod
    def get_commits_for_history(cls, history_id, project, warehouse):
        """Lấy danh sách commit liên quan đến một bản ghi history"""
        # Lấy thông tin environment của bản ghi history
        history = History.query.get(history_id)
        if not history:
            return []

        # Tạo query cơ bản
        query = cls.query.filter_by(project=project, warehouse=warehouse)

        # Nếu environment là STABLE, tìm các commit có history_prd_id = history_id
        if history.environment == 'STABLE':
            return query.filter_by(history_prd_id=history_id).all()
        # Nếu environment là UAT*, tìm các commit có history_uat_id = history_id
        elif history.environment.startswith('UAT'):
            return query.filter_by(history_uat_id=history_id).all()

        return []

    @classmethod
    def get_commits_for_history_id(cls, history_id):
        """Lấy danh sách commit cho một History ID cụ thể"""
        # Lấy thông tin history
        history = History.query.get(history_id)
        if not history:
            return []

        # Xác định điều kiện tìm kiếm dựa trên environment
        if history.environment == 'STABLE':
            # Nếu environment là STABLE, tìm các commit có history_prd_id = history_id
            commits = cls.query.filter_by(
                project=history.project,
                warehouse=history.warehouse,
                history_prd_id=history_id
            ).all()
        elif history.environment.startswith('UAT'):
            # Nếu environment là UAT*, tìm các commit có history_uat_id = history_id
            commits = cls.query.filter_by(
                project=history.project,
                warehouse=history.warehouse,
                history_uat_id=history_id
            ).all()
        else:
            # Trường hợp khác, không có commit nào
            return []

        result = []
        for i, commit in enumerate(commits):
            # Tạo đối tượng kết quả
            commit_info = {
                'stt': i + 1,
                'id': commit.id,
                'commit_sha': commit.commit_sha,
                'uat': commit.history_uat_id is not None and commit.history_uat_id > 0,
                'prd': commit.history_prd_id is not None and commit.history_prd_id > 0
            }

            result.append(commit_info)

        return result

    @classmethod
    def get_commits_not_in_prd(cls, project, warehouse):
        """Lấy danh sách commit chưa lên PRD theo project và warehouse"""
        if not project or not warehouse:
            return []

        # Tìm các commit có history_prd_id là null hoặc 0, sắp xếp theo commit.id tăng dần
        commits = cls.query.filter(
            cls.project == project,
            cls.warehouse == warehouse,
            (cls.history_prd_id == None) | (cls.history_prd_id == 0)
        ).order_by(cls.id.asc()).all()

        # Lấy danh sách history_uat_id để tìm thông tin message
        history_uat_ids = [commit.history_uat_id for commit in commits if commit.history_uat_id]
        history_uat_ids = list(set(history_uat_ids))
        #log.info(f"History UAT IDs: {history_uat_ids}")
        # Lấy thông tin history tương ứng
        histories = {}
        if history_uat_ids:
            history_records = History.query.filter(History.id.in_(history_uat_ids)).all()
            for history in history_records:
                histories[history.id] = history

        result = []
        for i, commit in enumerate(commits):
            # Lấy thông tin message từ history nếu có
            message = ""
            if commit.history_uat_id and commit.history_uat_id in histories:
                message = histories[commit.history_uat_id].message

            # Tạo đối tượng kết quả
            commit_info = {
                'stt': i + 1,
                'id': commit.id,
                'commit_sha': commit.commit_sha,
                'uat': commit.history_uat_id is not None and commit.history_uat_id > 0,
                'prd': False,  # Luôn là False vì đang tìm các commit chưa lên PRD
                'message': message,
                'history_uat_id': commit.history_uat_id
            }

            result.append(commit_info)
            #log.info(f"Commit info: {commit_info}")

        return result
    
    @classmethod
    def filter_message_by_prd_status(cls, message, commit_prd_status):
        """Lọc message chỉ giữ lại thông tin của các commit chưa lên PRD"""
        if not message:
            return ""
        
        lines = message.split('\n')
        filtered_lines = []
        skip_next_commit = False
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # Sử dụng biểu thức chính quy để tìm các chuỗi có độ dài 40 ký tự (SHA-1)
            sha_pattern = r'\b[a-f0-9]{40}\b'
            shas = re.findall(sha_pattern, line)
            
            # Nếu dòng chứa commit SHA
            if shas:
                commit_sha = shas[0]  # Lấy SHA đầu tiên tìm được
                # Kiểm tra commit này đã lên PRD chưa
                if commit_sha in commit_prd_status and commit_prd_status[commit_sha]:
                    # Commit đã lên PRD, bỏ qua
                    skip_next_commit = True
                    # Xóa dòng mô tả trước đó nếu có (không chứa SHA)
                    if filtered_lines and not re.findall(sha_pattern, filtered_lines[-1]):
                        filtered_lines.pop()
                    continue
                else:
                    # Commit chưa lên PRD, giữ lại
                    skip_next_commit = False
                    filtered_lines.append(line)
            else:
                # Nếu không phải dòng commit và không bị skip
                if not skip_next_commit:
                    filtered_lines.append(line)
        
        return '\n'.join(filtered_lines)