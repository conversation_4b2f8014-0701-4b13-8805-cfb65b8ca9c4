from flask import Flask
from config import Config
from app.database import db, init_app

def create_app(config_class=Config):
    app = Flask(__name__)
    app.config.from_object(config_class)

    # Initialize database
    db.init_app(app)

    # Register blueprints
    from app.routes import main_bp
    app.register_blueprint(main_bp)

    # Initialize database tables
    with app.app_context():
        db.create_all()

    return app
