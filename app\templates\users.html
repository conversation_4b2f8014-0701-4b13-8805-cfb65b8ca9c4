{% extends "base.html" %}

{% block title %}Users{% endblock %}

{% block content %}
<div class="bg-white p-6 rounded-lg shadow-md">
    <h1 class="text-3xl font-bold text-blue-600 mb-4">Users</h1>



    {% if users %}
        <div class="overflow-x-auto">
            <table class="min-w-full bg-white">
                <thead class="bg-gray-100">
                    <tr>
                        <th class="py-2 px-4 border-b text-left">ID</th>
                        <th class="py-2 px-4 border-b text-left">Username</th>
                        <th class="py-2 px-4 border-b text-left">Email</th>
                        <th class="py-2 px-4 border-b text-left">Created At</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                        <tr class="hover:bg-gray-50">
                            <td class="py-2 px-4 border-b">{{ user.id }}</td>
                            <td class="py-2 px-4 border-b">{{ user.username }}</td>
                            <td class="py-2 px-4 border-b">{{ user.email }}</td>
                            <td class="py-2 px-4 border-b">{{ user.created_at }}</td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% else %}
        <p class="text-gray-600">No users found.</p>
    {% endif %}

    <div class="mt-4">
        <a href="{{ url_for('main.index') }}" class="text-blue-600 hover:underline">Back to Home</a>
    </div>
</div>
{% endblock %}
